{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineSystem.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.3.11579264/build/cmake/android-legacy.toolchain.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.3.11579264/build/cmake/platforms.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.3.11579264/build/cmake/compiler_id.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "C:/Users/<USER>/Downloads/codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios/Flutter Source Code/project/listplace/android/app/.cxx/RelWithDebInfo/05v6m661/x86_64/CMakeFiles/3.18.1-g262b901-dirty/CMakeSystem.cmake"}, {"isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android/Determine-Compiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "C:/Users/<USER>/Downloads/codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios/Flutter Source Code/project/listplace/android/app/.cxx/RelWithDebInfo/05v6m661/x86_64/CMakeFiles/3.18.1-g262b901-dirty/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Determine-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android/Determine-Compiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "C:/Users/<USER>/Downloads/codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios/Flutter Source Code/project/listplace/android/app/.cxx/RelWithDebInfo/05v6m661/x86_64/CMakeFiles/3.18.1-g262b901-dirty/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Internal/CMakeCheckCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCCompilerABI.c"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompileFeatures.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "C:/Users/<USER>/Downloads/codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios/Flutter Source Code/project/listplace/android/app/.cxx/RelWithDebInfo/05v6m661/x86_64/CMakeFiles/3.18.1-g262b901-dirty/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Platform/Android-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeDetermineCompileFeatures.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.18.1/share/cmake-3.18/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "C:/Users/<USER>/Downloads/codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios/Flutter Source Code/project/listplace/android/app/.cxx/RelWithDebInfo/05v6m661/x86_64/CMakeFiles/3.18.1-g262b901-dirty/CMakeCXXCompiler.cmake"}], "kind": "cmakeFiles", "paths": {"build": "C:/Users/<USER>/Downloads/codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios/Flutter Source Code/project/listplace/android/app/.cxx/RelWithDebInfo/05v6m661/x86_64", "source": "D:/flutter_windows_3.29.2-stable/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 1, "minor": 0}}