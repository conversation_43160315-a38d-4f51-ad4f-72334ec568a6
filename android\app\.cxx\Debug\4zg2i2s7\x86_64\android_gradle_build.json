{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.18.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios\\Flutter Source Code\\project\\listplace\\android\\app\\.cxx\\Debug\\4zg2i2s7\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.18.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios\\Flutter Source Code\\project\\listplace\\android\\app\\.cxx\\Debug\\4zg2i2s7\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}