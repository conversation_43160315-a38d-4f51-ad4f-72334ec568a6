import 'dart:ui';

class AppColors {
  static Color mainColor = Color(0xffDAFF3F);

  static const Color mainColorWithOpacity = Color(0xffFFF8F7);

  static const Color textFieldHintColor = Color(0xffB4B6B8);
  static const Color greyColor = Color(0xff818688);
  static const Color darkBgColor = Color(0xff0E1621);
  static const Color darkCardColor = Color(0xff17212B);
  static const Color scaffoldColor = Color(0xffF9FAFC);
  static const Color fillColor2 = Color(0xffF7F7F5);

  static const Color sliderInActiveColor = Color(0xffEAEAEA);
  static const Color darkCardColorDeep = Color(0xff25303D);
  static const Color paragraphColor = Color(0xff717171);
  static Color pendingColor = Color(0xffF7931A);

  static const Color whiteColor = Color(0xffFFFFFF);
  static const Color blackColor = Color(0xff040D12);
  static const Color black5 = Color(0xffF2F3F3);
  static const Color black10 = Color(0xffE6E7E7);
  static const Color black20 = Color(0xffCDCFD0);
  static const Color black30 = Color(0xffB4B6B8);
  static const Color black40 = Color(0xff9A9D9F);
  static const Color black50 = Color(0xff818688);
  static const Color black60 = Color(0xff686E71);
  static const Color black70 = Color(0xff494D4F);
  static const Color black80 = Color(0xff363D41);
  static const Color redColor = Color(0xFFFF6464);
  static const Color greenColor = Color(0xff12B600);

  static const Color starColor = Color(0xffFFC107);
  static const Color activeColor = Color(0xff00A97F);
  static const Color approvedColor = Color(0xff0DCAF0);

  static const Color dividerColor = Color(0xffF2F3F3);
  static const Color fillColor = Color(0xffF5F7F9);
  static const Color activeStatusColor = Color(0xff00A97F);
  static const Color approvedStatusColor = Color(0xff0DCAF0);
}
