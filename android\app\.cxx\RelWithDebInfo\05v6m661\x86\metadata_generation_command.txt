                    -HD:\flutter_windows_3.29.2-stable\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-D<PERSON>DROID_PLATFORM=android-23
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.18.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Downloads\codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios\Flutter Source Code\project\listplace\build\app\intermediates\cxx\RelWithDebInfo\05v6m661\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Downloads\codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios\Flutter Source Code\project\listplace\build\app\intermediates\cxx\RelWithDebInfo\05v6m661\obj\x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BC:\Users\<USER>\Downloads\codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios\Flutter Source Code\project\listplace\android\app\.cxx\RelWithDebInfo\05v6m661\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                    Build command args: []
                    Version: 2