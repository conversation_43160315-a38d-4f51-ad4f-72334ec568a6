{"buildFiles": ["/Users/<USER>/Downloads/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja", "-C", "/Users/<USER>/Downloads/bug-finder-project/LISTPLACE-APP/listplace/android/app/.cxx/RelWithDebInfo/1w165h1e/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja", "-C", "/Users/<USER>/Downloads/bug-finder-project/LISTPLACE-APP/listplace/android/app/.cxx/RelWithDebInfo/1w165h1e/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}