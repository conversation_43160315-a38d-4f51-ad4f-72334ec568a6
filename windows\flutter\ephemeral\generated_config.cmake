# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\Downloads\\codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios\\Flutter Source Code\\project\\listplace" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\Downloads\\codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios\\Flutter Source Code\\project\\listplace"
  "FLUTTER_ROOT=D:\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\Downloads\\codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios\\Flutter Source Code\\project\\listplace\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\Downloads\\codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios\\Flutter Source Code\\project\\listplace"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\Downloads\\codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios\\Flutter Source Code\\project\\listplace\\lib\\main.dart"
  "DART_DEFINES=Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\Downloads\\codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios\\Flutter Source Code\\project\\listplace\\.dart_tool\\package_config.json"
)
