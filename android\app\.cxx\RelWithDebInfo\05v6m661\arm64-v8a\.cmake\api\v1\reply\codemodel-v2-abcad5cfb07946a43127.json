{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Downloads/codecanyon-57380835-listplace-business-directory-listing-flutter-app-android-ios/Flutter Source Code/project/listplace/android/app/.cxx/RelWithDebInfo/05v6m661/arm64-v8a", "source": "D:/flutter_windows_3.29.2-stable/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 1}}